import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../lib/core/constants/payment_test_config.dart';
import '../../lib/core/utils/logger.dart';

/// Utility class for manual testing of Zoho Payment API
class PaymentTestUtils {
  static final AppLogger _logger = AppLogger();
  static late Dio _dio;

  /// Initialize the test utilities
  static void initialize({String? customBaseUrl, String? authToken}) {
    final baseUrl = customBaseUrl ?? PaymentTestConfig.currentApiBaseUrl;
    
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (authToken != null) 'Authorization': 'Bearer $authToken',
      },
    ));

    // Add logging interceptor for debugging
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => _logger.d(obj.toString()),
    ));

    _logger.i('PaymentTestUtils initialized with base URL: $baseUrl');
  }

  /// Test payment session creation
  static Future<Map<String, dynamic>> testCreatePaymentSession({
    double? amount,
    String? invoiceNumber,
    String? customerId,
    String? description,
  }) async {
    _logger.i('🧪 Testing payment session creation...');

    final requestData = {
      'amount': amount ?? PaymentTestConfig.testAmountMedium,
      'currency': 'INR',
      'invoice_number': invoiceNumber ?? PaymentTestConfig.generateTestInvoiceNumber(),
      'customer_id': customerId ?? PaymentTestConfig.testCustomerId,
      'description': description ?? PaymentTestConfig.testInvoiceDescription,
      'customer_name': PaymentTestConfig.testCustomerName,
      'customer_email': PaymentTestConfig.testCustomerEmail,
      'customer_phone': PaymentTestConfig.testCustomerPhone,
    };

    try {
      final response = await _dio.post(
        '/zoho/payments/create-session',
        data: requestData,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        _logger.i('✅ Payment session created successfully');
        _logger.i('Session ID: ${responseData['data']['payment_session_id']}');
        _logger.i('Payment URL: ${responseData['data']['payment_url']}');
        return responseData;
      } else {
        _logger.e('❌ Failed to create payment session: ${response.statusCode}');
        throw Exception('HTTP ${response.statusCode}: ${response.statusMessage}');
      }
    } catch (e) {
      _logger.e('❌ Error creating payment session: $e');
      rethrow;
    }
  }

  /// Test payment status checking
  static Future<Map<String, dynamic>> testCheckPaymentStatus(String sessionId) async {
    _logger.i('🧪 Testing payment status for session: $sessionId');

    try {
      final response = await _dio.get('/zoho/payments/status/$sessionId');

      if (response.statusCode == 200) {
        final responseData = response.data;
        final status = responseData['data']['payment_session']['status'];
        _logger.i('✅ Payment status retrieved: $status');
        return responseData;
      } else {
        _logger.e('❌ Failed to check payment status: ${response.statusCode}');
        throw Exception('HTTP ${response.statusCode}: ${response.statusMessage}');
      }
    } catch (e) {
      _logger.e('❌ Error checking payment status: $e');
      rethrow;
    }
  }

  /// Test payment verification
  static Future<Map<String, dynamic>> testVerifyPayment(String sessionId) async {
    _logger.i('🧪 Testing payment verification for session: $sessionId');

    try {
      final response = await _dio.get('/zoho/payments/status/$sessionId');

      if (response.statusCode == 200) {
        final responseData = response.data;
        _logger.i('✅ Payment verification completed');
        return responseData;
      } else {
        _logger.e('❌ Failed to verify payment: ${response.statusCode}');
        throw Exception('HTTP ${response.statusCode}: ${response.statusMessage}');
      }
    } catch (e) {
      _logger.e('❌ Error verifying payment: $e');
      rethrow;
    }
  }

  /// Test error scenarios
  static Future<void> testErrorScenarios() async {
    _logger.i('🧪 Testing error scenarios...');

    // Test 1: Invalid amount
    try {
      await testCreatePaymentSession(amount: -100.0);
      _logger.w('⚠️ Expected error for negative amount, but request succeeded');
    } catch (e) {
      _logger.i('✅ Negative amount error handled correctly: $e');
    }

    // Test 2: Empty invoice number
    try {
      await testCreatePaymentSession(invoiceNumber: '');
      _logger.w('⚠️ Expected error for empty invoice number, but request succeeded');
    } catch (e) {
      _logger.i('✅ Empty invoice number error handled correctly: $e');
    }

    // Test 3: Invalid session ID
    try {
      await testCheckPaymentStatus('INVALID_SESSION_ID');
      _logger.w('⚠️ Expected error for invalid session ID, but request succeeded');
    } catch (e) {
      _logger.i('✅ Invalid session ID error handled correctly: $e');
    }

    _logger.i('✅ Error scenario testing completed');
  }

  /// Run comprehensive test suite
  static Future<void> runComprehensiveTests() async {
    _logger.i('🚀 Starting comprehensive payment API tests...');

    try {
      // Test 1: Create payment session
      final sessionResponse = await testCreatePaymentSession();
      final sessionId = sessionResponse['data']['payment_session_id'] as String;

      // Test 2: Check payment status
      await testCheckPaymentStatus(sessionId);

      // Test 3: Verify payment
      await testVerifyPayment(sessionId);

      // Test 4: Error scenarios
      await testErrorScenarios();

      // Test 5: Performance test
      await testPerformance();

      _logger.i('🎉 All comprehensive tests completed successfully!');
    } catch (e) {
      _logger.e('❌ Comprehensive tests failed: $e');
      rethrow;
    }
  }

  /// Test API performance
  static Future<void> testPerformance() async {
    _logger.i('🧪 Testing API performance...');

    const testCount = 5;
    final durations = <Duration>[];

    for (int i = 0; i < testCount; i++) {
      final stopwatch = Stopwatch()..start();
      
      try {
        await testCreatePaymentSession(
          invoiceNumber: 'PERF_TEST_${DateTime.now().millisecondsSinceEpoch}_$i',
        );
        stopwatch.stop();
        durations.add(stopwatch.elapsed);
        _logger.i('Test $i completed in ${stopwatch.elapsed.inMilliseconds}ms');
      } catch (e) {
        stopwatch.stop();
        _logger.e('Test $i failed: $e');
      }
    }

    if (durations.isNotEmpty) {
      final avgDuration = durations.fold<Duration>(
        Duration.zero,
        (prev, duration) => prev + duration,
      ) ~/ durations.length;

      _logger.i('✅ Performance test completed:');
      _logger.i('   Average duration: ${avgDuration.inMilliseconds}ms');
      _logger.i('   Min duration: ${durations.map((d) => d.inMilliseconds).reduce((a, b) => a < b ? a : b)}ms');
      _logger.i('   Max duration: ${durations.map((d) => d.inMilliseconds).reduce((a, b) => a > b ? a : b)}ms');
    }
  }

  /// Generate test report
  static Future<void> generateTestReport() async {
    _logger.i('📊 Generating test report...');

    final report = StringBuffer();
    report.writeln('# Zoho Payment API Test Report');
    report.writeln('Generated: ${DateTime.now().toIso8601String()}');
    report.writeln('Environment: ${PaymentTestConfig.isTestEnvironment ? 'Test' : 'Production'}');
    report.writeln('Base URL: ${PaymentTestConfig.currentApiBaseUrl}');
    report.writeln();

    try {
      // Run tests and collect results
      final sessionResponse = await testCreatePaymentSession();
      final sessionId = sessionResponse['data']['payment_session_id'] as String;

      report.writeln('## Test Results');
      report.writeln('✅ Payment session creation: PASSED');
      report.writeln('   Session ID: $sessionId');

      final statusResponse = await testCheckPaymentStatus(sessionId);
      final status = statusResponse['data']['payment_session']['status'];
      report.writeln('✅ Payment status check: PASSED');
      report.writeln('   Status: $status');

      report.writeln('✅ Payment verification: PASSED');

      // Save report to file
      final file = File('test_reports/payment_api_test_report_${DateTime.now().millisecondsSinceEpoch}.md');
      await file.create(recursive: true);
      await file.writeAsString(report.toString());

      _logger.i('📄 Test report saved to: ${file.path}');
    } catch (e) {
      report.writeln('❌ Test execution failed: $e');
      _logger.e('❌ Failed to generate test report: $e');
    }
  }

  /// Validate API response structure
  static bool validatePaymentSessionResponse(Map<String, dynamic> response) {
    try {
      final data = response['data'];
      final paymentSession = data['payment_session'];

      // Check required fields
      final requiredFields = [
        'payments_session_id',
        'payment_url',
        'amount',
        'currency',
        'status',
        'created_time',
      ];

      for (final field in requiredFields) {
        if (!paymentSession.containsKey(field)) {
          _logger.e('❌ Missing required field: $field');
          return false;
        }
      }

      // Validate data types
      if (paymentSession['amount'] is! num) {
        _logger.e('❌ Amount is not a number');
        return false;
      }

      if (paymentSession['payment_url'] is! String || 
          (paymentSession['payment_url'] as String).isEmpty) {
        _logger.e('❌ Invalid payment URL');
        return false;
      }

      _logger.i('✅ Payment session response validation passed');
      return true;
    } catch (e) {
      _logger.e('❌ Response validation failed: $e');
      return false;
    }
  }

  /// Print test configuration
  static void printTestConfiguration() {
    _logger.i('🔧 Test Configuration:');
    _logger.i('   Environment: ${PaymentTestConfig.isTestEnvironment ? 'Test' : 'Production'}');
    _logger.i('   API Base URL: ${PaymentTestConfig.currentApiBaseUrl}');
    _logger.i('   Zoho Base URL: ${PaymentTestConfig.sandboxBaseUrl}');
    _logger.i('   Test Customer ID: ${PaymentTestConfig.testCustomerId}');
    _logger.i('   Test Amounts: ${PaymentTestConfig.testAmountSmall}, ${PaymentTestConfig.testAmountMedium}, ${PaymentTestConfig.testAmountLarge}');
  }
}
