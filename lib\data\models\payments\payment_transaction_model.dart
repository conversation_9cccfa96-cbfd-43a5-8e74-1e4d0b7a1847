import '../../../domain/entities/payments/payment_transaction.dart';

/// Data model for payment transaction API responses
class PaymentTransactionModel extends PaymentTransaction {
  const PaymentTransactionModel({
    required super.transactionId,
    required super.sessionId,
    required super.invoiceNumber,
    required super.customerId,
    required super.amount,
    required super.currency,
    required super.status,
    required super.paymentMethod,
    required super.transactionDate,
    super.gatewayTransactionId,
    super.gatewayResponse,
    super.failureReason,
    super.metadata,
  });

  /// Creates a PaymentTransactionModel from JSON response
  factory PaymentTransactionModel.fromJson(Map<String, dynamic> json) {
    final transaction = json['transaction'] ?? json;
    final paymentSession = json['payment_session'] ?? json;
    
    return PaymentTransactionModel(
      transactionId: transaction['id'] ?? 
                    transaction['transaction_id'] ?? 
                    json['transaction_id'] ?? '',
      sessionId: paymentSession['payments_session_id'] ?? 
                transaction['payment_session_id'] ?? 
                json['payment_session_id'] ?? '',
      invoiceNumber: transaction['invoice_number'] ?? 
                    paymentSession['invoice_number'] ?? '',
      customerId: transaction['customer_id'] ?? 
                 paymentSession['customer_id'] ?? '',
      amount: (paymentSession['amount'] ?? transaction['amount'] ?? 0).toDouble(),
      currency: paymentSession['currency'] ?? transaction['currency'] ?? 'INR',
      status: _parseTransactionStatus(
        paymentSession['status'] ?? transaction['status'] ?? 'pending'
      ),
      paymentMethod: _parsePaymentMethod(
        paymentSession['payment_method'] ?? transaction['payment_method']
      ),
      transactionDate: _parseDateTime(
        paymentSession['completed_time'] ?? 
        transaction['completed_time'] ?? 
        transaction['created_at'] ?? 
        paymentSession['created_time']
      ),
      gatewayTransactionId: paymentSession['payment_id'] ?? 
                           transaction['payment_id'],
      gatewayResponse: transaction['gateway_response'] ?? 
                      paymentSession['gateway_response'],
      failureReason: paymentSession['error_message'] ?? 
                    transaction['error_message'] ?? 
                    transaction['failure_reason'],
      metadata: _parseMetadata(transaction['metadata'] ?? transaction['meta_data']),
    );
  }

  /// Creates a PaymentTransactionModel from payment status response
  factory PaymentTransactionModel.fromPaymentStatus(Map<String, dynamic> json) {
    final paymentSession = json['payment_session'] ?? json;
    final transaction = json['transaction'] ?? {};
    
    return PaymentTransactionModel(
      transactionId: transaction['id'] ?? 
                    json['transaction_id'] ?? 
                    paymentSession['payments_session_id'] ?? '',
      sessionId: paymentSession['payments_session_id'] ?? '',
      invoiceNumber: transaction['invoice_number'] ?? 
                    paymentSession['invoice_number'] ?? '',
      customerId: transaction['customer_id'] ?? 
                 paymentSession['customer_id'] ?? '',
      amount: (paymentSession['amount'] ?? 0).toDouble(),
      currency: paymentSession['currency'] ?? 'INR',
      status: _parseTransactionStatus(paymentSession['status'] ?? 'pending'),
      paymentMethod: _parsePaymentMethod(paymentSession['payment_method']),
      transactionDate: _parseDateTime(
        paymentSession['completed_time'] ?? 
        paymentSession['created_time']
      ),
      gatewayTransactionId: paymentSession['payment_id'],
      gatewayResponse: null,
      failureReason: paymentSession['error_message'],
      metadata: null,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'transaction_id': transactionId,
      'payment_session_id': sessionId,
      'invoice_number': invoiceNumber,
      'customer_id': customerId,
      'amount': amount,
      'currency': currency,
      'status': status.name,
      'payment_method': paymentMethod.name,
      'transaction_date': transactionDate.toIso8601String(),
      if (gatewayTransactionId != null) 'gateway_transaction_id': gatewayTransactionId,
      if (gatewayResponse != null) 'gateway_response': gatewayResponse,
      if (failureReason != null) 'failure_reason': failureReason,
      if (metadata != null) 'metadata': metadata,
    };
  }

  static PaymentTransactionStatus _parseTransactionStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
      case 'created':
        return PaymentTransactionStatus.pending;
      case 'succeeded':
      case 'completed':
      case 'success':
        return PaymentTransactionStatus.success;
      case 'failed':
        return PaymentTransactionStatus.failed;
      case 'cancelled':
        return PaymentTransactionStatus.cancelled;
      case 'refunded':
        return PaymentTransactionStatus.refunded;
      default:
        return PaymentTransactionStatus.pending;
    }
  }

  static PaymentMethod _parsePaymentMethod(String? method) {
    if (method == null) return PaymentMethod.unknown;
    
    switch (method.toLowerCase()) {
      case 'card':
      case 'credit_card':
      case 'creditcard':
        return PaymentMethod.creditCard;
      case 'debit_card':
      case 'debitcard':
        return PaymentMethod.debitCard;
      case 'net_banking':
      case 'netbanking':
        return PaymentMethod.netBanking;
      case 'upi':
        return PaymentMethod.upi;
      case 'wallet':
        return PaymentMethod.wallet;
      default:
        return PaymentMethod.unknown;
    }
  }

  static DateTime _parseDateTime(dynamic timestamp) {
    if (timestamp == null) return DateTime.now();
    
    if (timestamp is int) {
      // Unix timestamp (seconds)
      return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    }
    
    if (timestamp is String) {
      return DateTime.tryParse(timestamp) ?? DateTime.now();
    }
    
    return DateTime.now();
  }

  static Map<String, dynamic>? _parseMetadata(dynamic metaData) {
    if (metaData == null) return null;
    
    if (metaData is Map<String, dynamic>) {
      return metaData;
    }
    
    return null;
  }
}
