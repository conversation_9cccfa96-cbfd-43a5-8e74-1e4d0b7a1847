import 'package:flutter_test/flutter_test.dart';
import '../lib/domain/entities/payments/payment_request.dart';
import '../lib/domain/entities/payments/payment_session.dart';
import '../lib/domain/entities/payments/payment_transaction.dart';

/// Quick test to verify payment entities are working correctly
void main() {
  group('Payment Entities Quick Test', () {
    test('PaymentRequest should be created correctly', () {
      // Arrange & Act
      const paymentRequest = PaymentRequest(
        amount: 100.0,
        currency: 'INR',
        invoiceNumber: 'INV-001',
        customerId: 'CUST-001',
        description: 'Test payment',
        customerName: 'Test Customer',
        customerEmail: '<EMAIL>',
      );

      // Assert
      expect(paymentRequest.amount, equals(100.0));
      expect(paymentRequest.currency, equals('INR'));
      expect(paymentRequest.invoiceNumber, equals('INV-001'));
      expect(paymentRequest.customerId, equals('CUST-001'));
      expect(paymentRequest.description, equals('Test payment'));
      expect(paymentRequest.customerName, equals('Test Customer'));
      expect(paymentRequest.customerEmail, equals('<EMAIL>'));
      expect(paymentRequest.isValid, isTrue);
    });

    test('PaymentRequest validation should work correctly', () {
      // Test invalid request
      const invalidRequest = PaymentRequest(
        amount: -100.0, // Invalid amount
        currency: '', // Invalid currency
        invoiceNumber: '',
        customerId: '',
      );

      expect(invalidRequest.isValid, isFalse);
      expect(invalidRequest.validationErrors, isNotEmpty);
      expect(
        invalidRequest.validationErrors,
        contains('Amount must be greater than 0'),
      );
      expect(invalidRequest.validationErrors, contains('Currency is required'));
      expect(
        invalidRequest.validationErrors,
        contains('Invoice number is required'),
      );
      expect(
        invalidRequest.validationErrors,
        contains('Customer ID is required'),
      );
    });

    test('PaymentSession should be created correctly', () {
      // Arrange & Act
      final paymentSession = PaymentSession(
        sessionId: 'PS_123456789',
        paymentUrl: 'https://payments.zoho.in/checkout/PS_123456789',
        amount: 100.0,
        currency: 'INR',
        invoiceNumber: 'INV-001',
        customerId: 'CUST-001',
        description: 'Test payment',
        status: PaymentSessionStatus.created,
        createdAt: DateTime(2024, 1, 1),
      );

      // Assert
      expect(paymentSession.sessionId, equals('PS_123456789'));
      expect(paymentSession.paymentUrl, contains('payments.zoho.in'));
      expect(paymentSession.amount, equals(100.0));
      expect(paymentSession.currency, equals('INR'));
      expect(paymentSession.status, equals(PaymentSessionStatus.created));
      expect(paymentSession.isActive, isTrue);
      expect(paymentSession.isExpired, isFalse);
    });

    test('PaymentTransaction should be created correctly', () {
      // Arrange & Act
      final paymentTransaction = PaymentTransaction(
        transactionId: 'TXN_123456789',
        sessionId: 'PS_123456789',
        invoiceNumber: 'INV-001',
        customerId: 'CUST-001',
        amount: 100.0,
        currency: 'INR',
        status: PaymentTransactionStatus.success,
        paymentMethod: PaymentMethod.creditCard,
        transactionDate: DateTime(2024, 1, 1),
      );

      // Assert
      expect(paymentTransaction.transactionId, equals('TXN_123456789'));
      expect(paymentTransaction.sessionId, equals('PS_123456789'));
      expect(paymentTransaction.amount, equals(100.0));
      expect(
        paymentTransaction.status,
        equals(PaymentTransactionStatus.success),
      );
      expect(
        paymentTransaction.paymentMethod,
        equals(PaymentMethod.creditCard),
      );
      expect(paymentTransaction.isSuccessful, isTrue);
      expect(paymentTransaction.isFailed, isFalse);
      expect(paymentTransaction.isPending, isFalse);
    });

    test('PaymentSessionStatus extensions should work correctly', () {
      // Test display names
      expect(PaymentSessionStatus.created.displayName, equals('Created'));
      expect(PaymentSessionStatus.pending.displayName, equals('Pending'));
      expect(PaymentSessionStatus.completed.displayName, equals('Completed'));
      expect(PaymentSessionStatus.failed.displayName, equals('Failed'));
      expect(PaymentSessionStatus.cancelled.displayName, equals('Cancelled'));
      expect(PaymentSessionStatus.expired.displayName, equals('Expired'));

      // Test terminal status
      expect(PaymentSessionStatus.created.isTerminal, isFalse);
      expect(PaymentSessionStatus.pending.isTerminal, isFalse);
      expect(PaymentSessionStatus.completed.isTerminal, isTrue);
      expect(PaymentSessionStatus.failed.isTerminal, isTrue);
      expect(PaymentSessionStatus.cancelled.isTerminal, isTrue);
      expect(PaymentSessionStatus.expired.isTerminal, isTrue);
    });

    test('PaymentMethod extensions should work correctly', () {
      expect(PaymentMethod.creditCard.displayName, equals('Credit Card'));
      expect(PaymentMethod.debitCard.displayName, equals('Debit Card'));
      expect(PaymentMethod.netBanking.displayName, equals('Net Banking'));
      expect(PaymentMethod.upi.displayName, equals('UPI'));
      expect(PaymentMethod.wallet.displayName, equals('Wallet'));
      expect(PaymentMethod.unknown.displayName, equals('Unknown'));
    });

    test('PaymentRequest toJson should work correctly', () {
      const paymentRequest = PaymentRequest(
        amount: 100.0,
        currency: 'INR',
        invoiceNumber: 'INV-001',
        customerId: 'CUST-001',
        description: 'Test payment',
        customerName: 'Test Customer',
        customerEmail: '<EMAIL>',
      );

      final json = paymentRequest.toJson();

      expect(json['amount'], equals(100.0));
      expect(json['currency'], equals('INR'));
      expect(json['invoice_number'], equals('INV-001'));
      expect(json['customer_id'], equals('CUST-001'));
      expect(json['description'], equals('Test payment'));
      expect(json['customer_name'], equals('Test Customer'));
      expect(json['customer_email'], equals('<EMAIL>'));
    });
  });
}
