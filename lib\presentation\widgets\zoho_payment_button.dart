import 'package:flutter/material.dart';

import '../../core/services/payment_service.dart';
import '../screens/zoho_payment_web_view.dart';

class ZohoPaymentButton extends StatefulWidget {
  final double amount;
  final String invoiceNumber;
  final String customerId;
  final String? description;
  final String? customerName;
  final String? customerEmail;
  final Function(bool success, String? transactionId) onPaymentComplete;
  final String buttonText;

  const ZohoPaymentButton({
    super.key,
    required this.amount,
    required this.invoiceNumber,
    required this.customerId,
    this.description,
    this.customerName,
    this.customerEmail,
    required this.onPaymentComplete,
    this.buttonText = 'Pay Now',
  });

  @override
  State<ZohoPaymentButton> createState() => _ZohoPaymentButtonState();
}

class _ZohoPaymentButtonState extends State<ZohoPaymentButton> {
  final PaymentService _paymentService = PaymentService(
    baseUrl:
        'https://partner.aquaconnect.blue', // Replace with your backend URL
  );
  bool _isLoading = false;

  Future<void> _initiatePayment() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _paymentService.createPaymentSession(
        amount: widget.amount,
        invoiceNumber: widget.invoiceNumber,
        customerId: widget.customerId,
        description: widget.description,
        customerName: widget.customerName,
        customerEmail: widget.customerEmail,
      );

      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;

      // Extract payment URL from response
      final paymentUrl =
          'https://payments.zoho.in/checkout/${response['data']['payment_session_id']}';
      final paymentSessionId = response['data']['payment_session_id'];

      // Navigate to payment WebView
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder:
              (context) => ZohoPaymentWebView(
                paymentUrl: paymentUrl,
                onPaymentComplete: (success, transactionId) {
                  Navigator.of(context).pop({
                    'success': success,
                    'transactionId': transactionId ?? paymentSessionId,
                  });
                },
              ),
        ),
      );

      if (result != null) {
        widget.onPaymentComplete(result['success'], result['transactionId']);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Payment initiation failed: $e')));

      widget.onPaymentComplete(false, null);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: _isLoading ? null : _initiatePayment,
      child:
          _isLoading
              ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
              : Text(widget.buttonText),
    );
  }
}
