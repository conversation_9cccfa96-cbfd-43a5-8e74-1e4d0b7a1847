import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../../core/mixins/analytics_mixin.dart';
import '../../domain/entities/payments/payment_request.dart';
import '../cubit/payments/payment_cubit.dart';
import '../cubit/payments/payment_state.dart';
import '../screens/zoho_payment_web_view.dart';

class ZohoPaymentButton extends StatefulWidget {
  final double amount;
  final String invoiceNumber;
  final String customerId;
  final String? description;
  final String? customerName;
  final String? customerEmail;
  final Function(bool success, String? transactionId) onPaymentComplete;
  final String buttonText;
  final String currency;

  const ZohoPaymentButton({
    super.key,
    required this.amount,
    required this.invoiceNumber,
    required this.customerId,
    this.description,
    this.customerName,
    this.customerEmail,
    required this.onPaymentComplete,
    this.buttonText = 'Pay Now',
    this.currency = 'INR',
  });

  @override
  State<ZohoPaymentButton> createState() => _ZohoPaymentButtonState();
}

class _ZohoPaymentButtonState extends State<ZohoPaymentButton>
    with AnalyticsMixin {
  late PaymentCubit _paymentCubit;

  @override
  void initState() {
    super.initState();
    _paymentCubit = GetIt.instance<PaymentCubit>();
  }

  Future<void> _initiatePayment() async {
    // Track payment initiation
    trackEvent(
      'payment_initiated',
      params: {
        'invoice_number': widget.invoiceNumber,
        'invoice_amount': widget.amount.toString(),
        'customer_id': widget.customerId,
        'payment_method': 'zoho_payments',
      },
    );

    // Create payment request
    final paymentRequest = PaymentRequest(
      amount: widget.amount,
      currency: widget.currency,
      invoiceNumber: widget.invoiceNumber,
      customerId: widget.customerId,
      description:
          widget.description ?? 'Payment for invoice ${widget.invoiceNumber}',
      customerName: widget.customerName,
      customerEmail: widget.customerEmail,
    );

    // Create payment session
    _paymentCubit.createPaymentSession(paymentRequest);
  }

  Future<void> _launchPaymentWebView(
    String paymentUrl,
    String sessionId,
  ) async {
    try {
      // Navigate to payment WebView
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder:
              (context) => ZohoPaymentWebView(
                paymentUrl: paymentUrl,
                onPaymentComplete: (success, transactionId) {
                  Navigator.of(context).pop({
                    'success': success,
                    'transactionId': transactionId ?? sessionId,
                  });
                },
              ),
        ),
      );

      if (result != null && mounted) {
        // Start polling for payment status after WebView returns
        _paymentCubit.startPaymentStatusPolling(sessionId);
      } else if (mounted) {
        // User closed WebView without completing payment
        _paymentCubit.cancelPayment(sessionId);
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to launch payment: $e');
        _paymentCubit.resetPayment();
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  String get screenName => 'payment_button';

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PaymentCubit, PaymentState>(
      bloc: _paymentCubit,
      listener: (context, state) {
        if (state is PaymentSessionCreated) {
          // Launch payment WebView when session is created
          _launchPaymentWebView(
            state.paymentSession.paymentUrl,
            state.paymentSession.sessionId,
          );
        } else if (state is PaymentSuccess) {
          // Track successful payment
          trackEvent(
            'payment_completed_successfully',
            params: {
              'invoice_number': widget.invoiceNumber,
              'invoice_amount': widget.amount.toString(),
              'transaction_id': state.transaction.transactionId,
              'payment_method': 'zoho_payments',
            },
          );

          _showSuccessSnackBar('Payment completed successfully!');
          widget.onPaymentComplete(true, state.transaction.transactionId);
        } else if (state is PaymentFailed) {
          // Track failed payment
          trackEvent(
            'payment_failed',
            params: {
              'invoice_number': widget.invoiceNumber,
              'invoice_amount': widget.amount.toString(),
              'session_id': state.sessionId,
              'failure_reason': state.reason,
            },
          );

          _showErrorSnackBar('Payment failed: ${state.reason}');
          widget.onPaymentComplete(false, null);
        } else if (state is PaymentCancelled) {
          // Track cancelled payment
          trackEvent(
            'payment_cancelled',
            params: {
              'invoice_number': widget.invoiceNumber,
              'session_id': state.sessionId,
            },
          );

          widget.onPaymentComplete(false, null);
        } else if (state is PaymentExpired) {
          _showErrorSnackBar('Payment session expired. Please try again.');
          widget.onPaymentComplete(false, null);
        } else if (state is PaymentError) {
          _showErrorSnackBar(state.message);
          widget.onPaymentComplete(false, null);
        }
      },
      builder: (context, state) {
        final isLoading = state.isLoading;
        final isProcessing = state is PaymentProcessing;

        return ElevatedButton(
          onPressed: isLoading ? null : _initiatePayment,
          child:
              isLoading
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                  : isProcessing
                  ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(state.statusMessage),
                    ],
                  )
                  : Text(widget.buttonText),
        );
      },
    );
  }

  @override
  void dispose() {
    _paymentCubit.resetPayment();
    super.dispose();
  }
}
