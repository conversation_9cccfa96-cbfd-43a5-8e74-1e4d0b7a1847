import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/payments/payment_transaction.dart';
import '../../repositories/payment_repository.dart';

/// Use case for verifying payment status and getting transaction details
class VerifyPaymentUseCase {
  final PaymentRepository repository;

  VerifyPaymentUseCase(this.repository);

  /// Verifies payment status and returns transaction details
  /// 
  /// Returns [PaymentTransaction] on success or [Failure] on error
  Future<Either<Failure, PaymentTransaction>> call(String sessionId) async {
    if (sessionId.isEmpty) {
      return Left(
        ValidationFailure(message: 'Session ID cannot be empty'),
      );
    }

    return await repository.verifyPayment(sessionId);
  }
}

/// Use case for checking payment status without full verification
class CheckPaymentStatusUseCase {
  final PaymentRepository repository;

  CheckPaymentStatusUseCase(this.repository);

  /// Checks payment status without full verification
  /// 
  /// Returns [PaymentSession] on success or [Failure] on error
  Future<Either<Failure, PaymentSession>> call(String sessionId) async {
    if (sessionId.isEmpty) {
      return Left(
        ValidationFailure(message: 'Session ID cannot be empty'),
      );
    }

    return await repository.checkPaymentStatus(sessionId);
  }
}

/// Use case for getting payment session details
class GetPaymentSessionUseCase {
  final PaymentRepository repository;

  GetPaymentSessionUseCase(this.repository);

  /// Gets payment session by ID
  /// 
  /// Returns [PaymentSession] on success or [Failure] on error
  Future<Either<Failure, PaymentSession>> call(String sessionId) async {
    if (sessionId.isEmpty) {
      return Left(
        ValidationFailure(message: 'Session ID cannot be empty'),
      );
    }

    return await repository.getPaymentSession(sessionId);
  }
}
