import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/payments/payment_request.dart';
import '../../../domain/entities/payments/payment_session.dart';
import '../../../domain/entities/payments/payment_transaction.dart';
import '../../../domain/usecases/payments/create_payment_session_usecase.dart';
import '../../../domain/usecases/payments/verify_payment_usecase.dart';
import 'payment_state.dart';

/// Cubit for managing payment operations
class PaymentCubit extends Cubit<PaymentState> {
  final CreatePaymentSessionUseCase _createPaymentSession;
  final VerifyPaymentUseCase _verifyPayment;
  final CheckPaymentStatusUseCase _checkPaymentStatus;
  final AppLogger _logger;

  Timer? _statusPollingTimer;
  static const Duration _pollingInterval = Duration(seconds: 3);
  static const Duration _pollingTimeout = Duration(minutes: 5);

  PaymentCubit({
    required CreatePaymentSessionUseCase createPaymentSession,
    required VerifyPaymentUseCase verifyPayment,
    required CheckPaymentStatusUseCase checkPaymentStatus,
    required AppLogger logger,
  })  : _createPaymentSession = createPaymentSession,
        _verifyPayment = verifyPayment,
        _checkPaymentStatus = checkPaymentStatus,
        _logger = logger,
        super(PaymentInitial());

  /// Creates a payment session for the given request
  Future<void> createPaymentSession(PaymentRequest request) async {
    try {
      _logger.i('Creating payment session for invoice: ${request.invoiceNumber}');
      emit(PaymentLoading());

      final result = await _createPaymentSession(request);

      result.fold(
        (failure) {
          _logger.e('Failed to create payment session: ${failure.toString()}');
          emit(PaymentError(
            message: _getErrorMessage(failure),
            failure: failure,
          ));
        },
        (paymentSession) {
          _logger.i('Payment session created successfully: ${paymentSession.sessionId}');
          emit(PaymentSessionCreated(paymentSession: paymentSession));
        },
      );
    } catch (e) {
      _logger.e('Unexpected error creating payment session: $e');
      emit(PaymentError(
        message: 'An unexpected error occurred while creating payment session',
      ));
    }
  }

  /// Starts payment status polling for the given session
  void startPaymentStatusPolling(String sessionId) {
    _logger.i('Starting payment status polling for session: $sessionId');
    
    // Cancel any existing timer
    _statusPollingTimer?.cancel();
    
    emit(PaymentProcessing(sessionId: sessionId));
    
    final startTime = DateTime.now();
    
    _statusPollingTimer = Timer.periodic(_pollingInterval, (timer) async {
      // Check for timeout
      if (DateTime.now().difference(startTime) > _pollingTimeout) {
        _logger.w('Payment status polling timeout for session: $sessionId');
        timer.cancel();
        emit(PaymentError(
          message: 'Payment verification timeout. Please check your payment status manually.',
        ));
        return;
      }

      await _checkPaymentStatusOnce(sessionId, timer);
    });
  }

  /// Checks payment status once
  Future<void> _checkPaymentStatusOnce(String sessionId, Timer timer) async {
    try {
      final result = await _checkPaymentStatus(sessionId);

      result.fold(
        (failure) {
          _logger.e('Failed to check payment status: ${failure.toString()}');
          // Don't emit error state during polling, just log and continue
        },
        (paymentSession) {
          _logger.d('Payment status: ${paymentSession.status.name}');
          
          // Check if payment is in a terminal state
          if (paymentSession.status.isTerminal) {
            timer.cancel();
            _handleTerminalPaymentStatus(paymentSession);
          } else {
            // Update processing state with current status
            emit(PaymentProcessing(
              sessionId: sessionId,
              currentStatus: paymentSession.status,
            ));
          }
        },
      );
    } catch (e) {
      _logger.e('Error during payment status polling: $e');
      // Continue polling on error
    }
  }

  /// Handles terminal payment status (completed, failed, cancelled, expired)
  void _handleTerminalPaymentStatus(PaymentSession paymentSession) {
    switch (paymentSession.status) {
      case PaymentSessionStatus.completed:
        _verifyCompletedPayment(paymentSession.sessionId);
        break;
      case PaymentSessionStatus.failed:
        _logger.w('Payment failed for session: ${paymentSession.sessionId}');
        emit(PaymentFailed(
          sessionId: paymentSession.sessionId,
          reason: 'Payment processing failed',
        ));
        break;
      case PaymentSessionStatus.cancelled:
        _logger.w('Payment cancelled for session: ${paymentSession.sessionId}');
        emit(PaymentCancelled(sessionId: paymentSession.sessionId));
        break;
      case PaymentSessionStatus.expired:
        _logger.w('Payment expired for session: ${paymentSession.sessionId}');
        emit(PaymentExpired(sessionId: paymentSession.sessionId));
        break;
      default:
        _logger.w('Unexpected terminal status: ${paymentSession.status}');
        emit(PaymentError(
          message: 'Unexpected payment status: ${paymentSession.status.displayName}',
        ));
    }
  }

  /// Verifies completed payment and gets transaction details
  Future<void> _verifyCompletedPayment(String sessionId) async {
    try {
      _logger.i('Verifying completed payment for session: $sessionId');
      
      final result = await _verifyPayment(sessionId);

      result.fold(
        (failure) {
          _logger.e('Failed to verify completed payment: ${failure.toString()}');
          emit(PaymentError(
            message: 'Payment completed but verification failed. Please contact support.',
          ));
        },
        (transaction) {
          _logger.i('Payment verified successfully: ${transaction.transactionId}');
          emit(PaymentSuccess(transaction: transaction));
        },
      );
    } catch (e) {
      _logger.e('Error verifying completed payment: $e');
      emit(PaymentError(
        message: 'Payment completed but verification failed. Please contact support.',
      ));
    }
  }

  /// Manually checks payment status (for user-triggered refresh)
  Future<void> checkPaymentStatus(String sessionId) async {
    try {
      _logger.i('Manually checking payment status for session: $sessionId');
      emit(PaymentLoading());

      final result = await _checkPaymentStatus(sessionId);

      result.fold(
        (failure) {
          _logger.e('Failed to check payment status: ${failure.toString()}');
          emit(PaymentError(
            message: _getErrorMessage(failure),
            failure: failure,
          ));
        },
        (paymentSession) {
          _logger.i('Payment status checked: ${paymentSession.status.name}');
          
          if (paymentSession.status.isTerminal) {
            _handleTerminalPaymentStatus(paymentSession);
          } else {
            emit(PaymentProcessing(
              sessionId: sessionId,
              currentStatus: paymentSession.status,
            ));
          }
        },
      );
    } catch (e) {
      _logger.e('Unexpected error checking payment status: $e');
      emit(PaymentError(
        message: 'An unexpected error occurred while checking payment status',
      ));
    }
  }

  /// Cancels payment session
  Future<void> cancelPayment(String sessionId) async {
    try {
      _logger.i('Cancelling payment session: $sessionId');
      
      // Stop polling
      _statusPollingTimer?.cancel();
      
      emit(PaymentCancelled(sessionId: sessionId));
    } catch (e) {
      _logger.e('Error cancelling payment: $e');
      emit(PaymentError(
        message: 'Failed to cancel payment',
      ));
    }
  }

  /// Resets payment state
  void resetPayment() {
    _logger.i('Resetting payment state');
    _statusPollingTimer?.cancel();
    emit(PaymentInitial());
  }

  /// Gets user-friendly error message from failure
  String _getErrorMessage(dynamic failure) {
    if (failure.toString().contains('network') || 
        failure.toString().contains('Network')) {
      return 'Network error. Please check your internet connection and try again.';
    } else if (failure.toString().contains('auth') || 
               failure.toString().contains('Auth')) {
      return 'Authentication error. Please log in again.';
    } else if (failure.toString().contains('validation') || 
               failure.toString().contains('Validation')) {
      return failure.toString().replaceAll('ValidationFailure: ', '');
    } else {
      return 'Payment service is temporarily unavailable. Please try again later.';
    }
  }

  @override
  Future<void> close() {
    _statusPollingTimer?.cancel();
    return super.close();
  }
}
