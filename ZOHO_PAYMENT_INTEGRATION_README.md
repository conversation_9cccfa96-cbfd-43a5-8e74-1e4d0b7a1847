# Zoho Payment Integration - AquaPartner Flutter Application

## Overview

This document describes the comprehensive Zoho payment integration implemented in the AquaPartner Flutter application. The integration follows clean architecture principles and provides secure, reliable payment processing for overdue invoices.

## Architecture

The payment integration follows the established clean architecture pattern with three main layers:

### Domain Layer
- **Entities**: `PaymentSession`, `PaymentTransaction`, `PaymentRequest`
- **Repository Interface**: `PaymentRepository`
- **Use Cases**: 
  - `CreatePaymentSessionUseCase`
  - `VerifyPaymentUseCase`
  - `CheckPaymentStatusUseCase`
  - `GetCustomerTransactionsUseCase`
  - `GetInvoiceTransactionsUseCase`

### Data Layer
- **Models**: `PaymentSessionModel`, `PaymentTransactionModel`
- **Remote Data Source**: `PaymentRemoteDataSourceImpl`
- **Repository Implementation**: `PaymentRepositoryImpl`

### Presentation Layer
- **Cubit**: `PaymentCubit` with comprehensive state management
- **States**: `PaymentInitial`, `PaymentLoading`, `PaymentSessionCreated`, `PaymentProcessing`, `PaymentSuccess`, `PaymentFailed`, etc.
- **Widgets**: Enhanced `ZohoPaymentButton` with real-time status updates

## Key Features

### 1. Secure Payment Processing
- Uses existing `ApiClient` with proper authentication headers
- All API calls include Bearer token authentication
- Request/response validation and error handling
- Secure WebView integration for payment completion

### 2. Real-time Payment Status Tracking
- Automatic status polling every 3 seconds
- Timeout handling (5-minute maximum)
- Terminal state detection (success, failed, cancelled, expired)
- User-friendly status messages and loading indicators

### 3. Comprehensive Error Handling
- Network error detection and retry mechanisms
- Authentication error handling
- Validation error messages
- User-friendly error notifications

### 4. Analytics Integration
- Payment initiation tracking
- Success/failure event tracking
- Performance monitoring
- User interaction analytics

### 5. Invoice Integration
- Automatic invoice refresh after payment completion
- Overdue invoice payment buttons
- Payment amount calculation (balance or total)
- Invoice status updates

## API Integration

The integration uses the Zoho Payment API with the following endpoints:

### Create Payment Session
```
POST /api/zoho/payments/create-session
```

### Check Payment Status
```
GET /api/zoho/payments/status/{sessionId}
```

### Verify Payment
```
GET /api/zoho/payments/status/{sessionId}
```

## Payment Flow

1. **Initiation**: User taps "Pay Now" button on overdue invoice
2. **Session Creation**: App creates payment session via API
3. **WebView Launch**: User redirected to Zoho payment page
4. **Status Polling**: App polls payment status every 3 seconds
5. **Completion**: Payment verified and invoice status updated
6. **Notification**: User receives success/failure feedback

## State Management

The `PaymentCubit` manages the following states:

- `PaymentInitial`: No payment operation in progress
- `PaymentLoading`: Creating payment session
- `PaymentSessionCreated`: Session created, launching WebView
- `PaymentProcessing`: Polling for payment status
- `PaymentSuccess`: Payment completed successfully
- `PaymentFailed`: Payment failed with reason
- `PaymentCancelled`: Payment cancelled by user
- `PaymentExpired`: Payment session expired
- `PaymentError`: Error occurred during process

## Security Considerations

### 1. Authentication
- All API calls use Bearer token authentication
- Tokens managed securely via `FlutterSecureStorage`
- Automatic token refresh handling

### 2. Data Validation
- Input validation for payment requests
- Response validation for API responses
- Amount and currency validation

### 3. Error Handling
- No sensitive data exposed in error messages
- Secure error logging without credentials
- Graceful degradation on failures

## Testing

### Unit Tests
- `CreatePaymentSessionUseCaseTest`: Use case validation and error handling
- `PaymentCubitTest`: State management and business logic
- Repository and data source tests

### Widget Tests
- `ZohoPaymentButtonTest`: UI behavior and user interactions
- State-based UI updates
- Error handling and user feedback

### Integration Tests
- End-to-end payment flow testing
- API integration testing
- Error scenario testing

## Configuration

### Dependencies
The payment system uses the following dependencies:
- `dio`: HTTP client for API calls
- `flutter_bloc`: State management
- `get_it`: Dependency injection
- `webview_flutter`: Payment page display
- `dartz`: Functional programming utilities

### Dependency Injection
Payment dependencies are registered in `lib/injection/payment_di.dart`:
- Data sources
- Repositories
- Use cases
- Cubits

## Usage

### Basic Implementation
```dart
ZohoPaymentButton(
  amount: invoice.balance > 0 ? invoice.balance : invoice.total,
  invoiceNumber: invoice.invoiceNumber,
  customerId: invoice.customerId,
  description: 'Payment for invoice ${invoice.invoiceNumber}',
  onPaymentComplete: (success, transactionId) {
    // Handle payment completion
    if (success) {
      // Refresh invoices
      context.read<InvoicesCubit>().loadInvoices();
    }
  },
  buttonText: 'Pay Now',
)
```

### Advanced Usage with Custom Handling
```dart
final paymentCubit = GetIt.instance<PaymentCubit>();

// Create payment request
final request = PaymentRequest(
  amount: 100.0,
  currency: 'INR',
  invoiceNumber: 'INV-001',
  customerId: 'CUST-001',
  description: 'Custom payment',
);

// Create payment session
paymentCubit.createPaymentSession(request);

// Listen to state changes
BlocListener<PaymentCubit, PaymentState>(
  bloc: paymentCubit,
  listener: (context, state) {
    if (state is PaymentSuccess) {
      // Handle success
    } else if (state is PaymentFailed) {
      // Handle failure
    }
  },
  child: YourWidget(),
)
```

## Monitoring and Debugging

### Logging
The payment system includes comprehensive logging:
- Payment session creation
- Status polling events
- Error conditions
- User interactions

### Analytics
Payment events are tracked for monitoring:
- `payment_initiated`
- `payment_completed_successfully`
- `payment_failed`
- `payment_cancelled`

### Performance Monitoring
- API response times
- Payment completion rates
- Error frequencies
- User interaction patterns

## Troubleshooting

### Common Issues

1. **Payment Session Creation Fails**
   - Check network connectivity
   - Verify authentication token
   - Validate payment request data

2. **Payment Status Not Updating**
   - Check polling mechanism
   - Verify API endpoint availability
   - Check for timeout issues

3. **WebView Not Loading**
   - Verify payment URL format
   - Check WebView permissions
   - Ensure network connectivity

### Debug Mode
Enable debug logging by setting log level to debug in `AppLogger`.

## Future Enhancements

1. **Offline Support**: Cache payment sessions for offline retry
2. **Multiple Payment Methods**: Support for different payment gateways
3. **Recurring Payments**: Automated payment scheduling
4. **Payment History**: Detailed transaction history view
5. **Refund Support**: Payment refund functionality

## Maintenance

### Regular Tasks
1. Monitor payment success rates
2. Update API endpoints as needed
3. Review error logs and fix issues
4. Update dependencies regularly
5. Test payment flow in staging environment

### Performance Optimization
1. Optimize polling intervals based on usage patterns
2. Implement caching for payment sessions
3. Reduce API call frequency where possible
4. Monitor and optimize WebView performance

## Support

For technical support or questions about the payment integration:
1. Check the logs for detailed error information
2. Review the API documentation
3. Test in staging environment first
4. Contact the development team for assistance

---

**Last Updated**: January 2024
**Version**: 1.0.0
**Maintainer**: AquaPartner Development Team
