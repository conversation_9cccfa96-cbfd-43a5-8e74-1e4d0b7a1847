import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/payments/payment_transaction.dart';
import '../../repositories/payment_repository.dart';

/// Use case for getting customer payment transactions
class GetCustomerTransactionsUseCase {
  final PaymentRepository repository;

  GetCustomerTransactionsUseCase(this.repository);

  /// Gets all payment transactions for a customer
  ///
  /// Returns [List<PaymentTransaction>] on success or [Failure] on error
  Future<Either<Failure, List<PaymentTransaction>>> call(
    String customerId, {
    int? limit,
    int? offset,
  }) async {
    if (customerId.isEmpty) {
      return Left(ValidationFailure('Customer ID cannot be empty'));
    }

    return await repository.getCustomerTransactions(
      customerId,
      limit: limit,
      offset: offset,
    );
  }
}

/// Use case for getting payment transactions for a specific invoice
class GetInvoiceTransactionsUseCase {
  final PaymentRepository repository;

  GetInvoiceTransactionsUseCase(this.repository);

  /// Gets payment transactions for a specific invoice
  ///
  /// Returns [List<PaymentTransaction>] on success or [Failure] on error
  Future<Either<Failure, List<PaymentTransaction>>> call(
    String invoiceNumber,
  ) async {
    if (invoiceNumber.isEmpty) {
      return Left(ValidationFailure('Invoice number cannot be empty'));
    }

    return await repository.getInvoiceTransactions(invoiceNumber);
  }
}

/// Use case for getting a specific payment transaction
class GetPaymentTransactionUseCase {
  final PaymentRepository repository;

  GetPaymentTransactionUseCase(this.repository);

  /// Gets payment transaction by transaction ID
  ///
  /// Returns [PaymentTransaction] on success or [Failure] on error
  Future<Either<Failure, PaymentTransaction>> call(String transactionId) async {
    if (transactionId.isEmpty) {
      return Left(ValidationFailure('Transaction ID cannot be empty'));
    }

    return await repository.getPaymentTransaction(transactionId);
  }
}
