import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import '../../../domain/entities/payments/payment_request.dart';
import '../../../domain/entities/payments/payment_session.dart';
import '../../../domain/repositories/payment_repository.dart';
import '../../../domain/usecases/payments/create_payment_session_usecase.dart';
import '../../../core/error/failures.dart';

import 'create_payment_session_usecase_test.mocks.dart';

@GenerateMocks([PaymentRepository])
void main() {
  late CreatePaymentSessionUseCase useCase;
  late MockPaymentRepository mockRepository;

  setUp(() {
    mockRepository = MockPaymentRepository();
    useCase = CreatePaymentSessionUseCase(mockRepository);
  });

  group('CreatePaymentSessionUseCase', () {
    const tPaymentRequest = PaymentRequest(
      amount: 100.0,
      currency: 'INR',
      invoiceNumber: 'INV-001',
      customerId: 'CUST-001',
      description: 'Test payment',
      customerName: 'Test Customer',
      customerEmail: '<EMAIL>',
    );

    const tPaymentSession = PaymentSession(
      sessionId: 'PS_123456789',
      paymentUrl: 'https://payments.zoho.in/checkout/PS_123456789',
      amount: 100.0,
      currency: 'INR',
      invoiceNumber: 'INV-001',
      customerId: 'CUST-001',
      description: 'Test payment',
      customerName: 'Test Customer',
      customerEmail: '<EMAIL>',
      status: PaymentSessionStatus.created,
      createdAt: DateTime(2024, 1, 1),
    );

    test('should create payment session successfully when request is valid', () async {
      // arrange
      when(mockRepository.createPaymentSession(any))
          .thenAnswer((_) async => const Right(tPaymentSession));

      // act
      final result = await useCase(tPaymentRequest);

      // assert
      expect(result, const Right(tPaymentSession));
      verify(mockRepository.createPaymentSession(tPaymentRequest));
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return ValidationFailure when request is invalid', () async {
      // arrange
      const invalidRequest = PaymentRequest(
        amount: -100.0, // Invalid amount
        currency: '',   // Invalid currency
        invoiceNumber: '',
        customerId: '',
      );

      // act
      final result = await useCase(invalidRequest);

      // assert
      expect(result, isA<Left<ValidationFailure, PaymentSession>>());
      verifyZeroInteractions(mockRepository);
    });

    test('should return ServerFailure when repository throws ServerException', () async {
      // arrange
      when(mockRepository.createPaymentSession(any))
          .thenAnswer((_) async => Left(ServerFailure()));

      // act
      final result = await useCase(tPaymentRequest);

      // assert
      expect(result, Left(ServerFailure()));
      verify(mockRepository.createPaymentSession(tPaymentRequest));
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return NetworkFailure when repository throws NetworkException', () async {
      // arrange
      when(mockRepository.createPaymentSession(any))
          .thenAnswer((_) async => Left(NetworkFailure()));

      // act
      final result = await useCase(tPaymentRequest);

      // assert
      expect(result, Left(NetworkFailure()));
      verify(mockRepository.createPaymentSession(tPaymentRequest));
      verifyNoMoreInteractions(mockRepository);
    });

    test('should validate required fields', () async {
      // arrange
      const requestWithMissingFields = PaymentRequest(
        amount: 100.0,
        currency: 'INR',
        invoiceNumber: '', // Missing
        customerId: '',    // Missing
      );

      // act
      final result = await useCase(requestWithMissingFields);

      // assert
      expect(result, isA<Left<ValidationFailure, PaymentSession>>());
      final failure = (result as Left).value as ValidationFailure;
      expect(failure.message, contains('Invoice number is required'));
      expect(failure.message, contains('Customer ID is required'));
      verifyZeroInteractions(mockRepository);
    });

    test('should validate email format when provided', () async {
      // arrange
      const requestWithInvalidEmail = PaymentRequest(
        amount: 100.0,
        currency: 'INR',
        invoiceNumber: 'INV-001',
        customerId: 'CUST-001',
        customerEmail: 'invalid-email', // Invalid email
      );

      // act
      final result = await useCase(requestWithInvalidEmail);

      // assert
      expect(result, isA<Left<ValidationFailure, PaymentSession>>());
      final failure = (result as Left).value as ValidationFailure;
      expect(failure.message, contains('Invalid email format'));
      verifyZeroInteractions(mockRepository);
    });

    test('should validate amount is positive', () async {
      // arrange
      const requestWithNegativeAmount = PaymentRequest(
        amount: -50.0, // Negative amount
        currency: 'INR',
        invoiceNumber: 'INV-001',
        customerId: 'CUST-001',
      );

      // act
      final result = await useCase(requestWithNegativeAmount);

      // assert
      expect(result, isA<Left<ValidationFailure, PaymentSession>>());
      final failure = (result as Left).value as ValidationFailure;
      expect(failure.message, contains('Amount must be greater than 0'));
      verifyZeroInteractions(mockRepository);
    });
  });
}
