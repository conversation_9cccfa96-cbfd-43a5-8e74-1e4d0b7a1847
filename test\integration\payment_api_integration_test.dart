import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';

import '../core/constants/payment_test_config.dart';
import '../core/network/api_client.dart';
import '../core/utils/logger.dart';
import '../data/datasources/remote/payment_remote_datasource.dart';
import '../domain/entities/payments/payment_request.dart';
import '../domain/repositories/payment_repository.dart';
import '../domain/usecases/payments/create_payment_session_usecase.dart';
import '../domain/usecases/payments/verify_payment_usecase.dart';

/// Integration tests for Zoho Payment API
/// 
/// These tests require:
/// 1. Backend server running with test endpoints
/// 2. Valid test API credentials
/// 3. Network connectivity
/// 
/// Run with: flutter test integration_test/payment_api_integration_test.dart
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Zoho Payment API Integration Tests', () {
    late PaymentRemoteDataSource dataSource;
    late PaymentRepository repository;
    late CreatePaymentSessionUseCase createPaymentSession;
    late VerifyPaymentUseCase verifyPayment;
    late AppLogger logger;

    setUpAll(() async {
      // Initialize test dependencies
      logger = AppLogger();
      
      // Create test API client
      final dio = Dio(BaseOptions(
        baseUrl: PaymentTestConfig.currentApiBaseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          // Add test authentication headers
          'Authorization': 'Bearer ${PaymentTestConfig.testApiKey}',
        },
      ));

      final apiClient = ApiClient(dio: dio, logger: logger);
      
      // Initialize data source and repository
      dataSource = PaymentRemoteDataSourceImpl(
        apiClient: apiClient,
        logger: logger,
      );
      
      // Note: You'll need to implement the repository
      // repository = PaymentRepositoryImpl(
      //   remoteDataSource: dataSource,
      //   networkInfo: networkInfo,
      //   logger: logger,
      // );
      
      // Initialize use cases
      // createPaymentSession = CreatePaymentSessionUseCase(repository);
      // verifyPayment = VerifyPaymentUseCase(repository);
    });

    group('Payment Session Creation', () {
      testWidgets('should create payment session successfully with valid data', (tester) async {
        // Arrange
        final paymentRequest = PaymentRequest(
          amount: PaymentTestConfig.testAmountMedium,
          currency: 'INR',
          invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
          customerId: PaymentTestConfig.testCustomerId,
          description: PaymentTestConfig.testInvoiceDescription,
          customerName: PaymentTestConfig.testCustomerName,
          customerEmail: PaymentTestConfig.testCustomerEmail,
        );

        // Act
        final paymentSession = await dataSource.createPaymentSession(paymentRequest);

        // Assert
        expect(paymentSession.sessionId, isNotEmpty);
        expect(paymentSession.paymentUrl, isNotEmpty);
        expect(paymentSession.amount, equals(PaymentTestConfig.testAmountMedium));
        expect(paymentSession.currency, equals('INR'));
        expect(paymentSession.invoiceNumber, equals(paymentRequest.invoiceNumber));
        expect(paymentSession.customerId, equals(PaymentTestConfig.testCustomerId));
        
        // Verify URL format
        expect(paymentSession.paymentUrl, contains('payments'));
        expect(paymentSession.paymentUrl, contains(paymentSession.sessionId));
        
        logger.i('✅ Payment session created: ${paymentSession.sessionId}');
      });

      testWidgets('should handle invalid payment request gracefully', (tester) async {
        // Arrange
        final invalidRequest = PaymentRequest(
          amount: -100.0, // Invalid amount
          currency: '', // Invalid currency
          invoiceNumber: '',
          customerId: '',
        );

        // Act & Assert
        expect(
          () => dataSource.createPaymentSession(invalidRequest),
          throwsA(isA<Exception>()),
        );
        
        logger.i('✅ Invalid request handled correctly');
      });

      testWidgets('should create session with different amounts', (tester) async {
        final testAmounts = [
          PaymentTestConfig.testAmountSmall,
          PaymentTestConfig.testAmountMedium,
          PaymentTestConfig.testAmountLarge,
        ];

        for (final amount in testAmounts) {
          // Arrange
          final paymentRequest = PaymentRequest(
            amount: amount,
            currency: 'INR',
            invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
            customerId: PaymentTestConfig.generateTestCustomerId(),
            description: 'Test payment for amount $amount',
          );

          // Act
          final paymentSession = await dataSource.createPaymentSession(paymentRequest);

          // Assert
          expect(paymentSession.amount, equals(amount));
          expect(paymentSession.sessionId, isNotEmpty);
          
          logger.i('✅ Payment session created for amount $amount: ${paymentSession.sessionId}');
        }
      });
    });

    group('Payment Status Checking', () {
      late String testSessionId;

      setUp(() async {
        // Create a test payment session first
        final paymentRequest = PaymentRequest(
          amount: PaymentTestConfig.testAmountSmall,
          currency: 'INR',
          invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
          customerId: PaymentTestConfig.testCustomerId,
          description: 'Test payment for status checking',
        );

        final paymentSession = await dataSource.createPaymentSession(paymentRequest);
        testSessionId = paymentSession.sessionId;
      });

      testWidgets('should check payment status successfully', (tester) async {
        // Act
        final paymentSession = await dataSource.checkPaymentStatus(testSessionId);

        // Assert
        expect(paymentSession.sessionId, equals(testSessionId));
        expect(paymentSession.status, isNotNull);
        
        logger.i('✅ Payment status checked: ${paymentSession.status.name}');
      });

      testWidgets('should handle non-existent session ID', (tester) async {
        // Arrange
        const nonExistentSessionId = 'NON_EXISTENT_SESSION_123';

        // Act & Assert
        expect(
          () => dataSource.checkPaymentStatus(nonExistentSessionId),
          throwsA(isA<Exception>()),
        );
        
        logger.i('✅ Non-existent session handled correctly');
      });
    });

    group('Payment Verification', () {
      testWidgets('should verify payment when session exists', (tester) async {
        // Arrange - Create a test payment session
        final paymentRequest = PaymentRequest(
          amount: PaymentTestConfig.testAmountSmall,
          currency: 'INR',
          invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
          customerId: PaymentTestConfig.testCustomerId,
          description: 'Test payment for verification',
        );

        final paymentSession = await dataSource.createPaymentSession(paymentRequest);

        // Act
        final transaction = await dataSource.verifyPayment(paymentSession.sessionId);

        // Assert
        expect(transaction.sessionId, equals(paymentSession.sessionId));
        expect(transaction.invoiceNumber, equals(paymentRequest.invoiceNumber));
        expect(transaction.customerId, equals(paymentRequest.customerId));
        expect(transaction.amount, equals(paymentRequest.amount));
        
        logger.i('✅ Payment verified: ${transaction.transactionId}');
      });
    });

    group('Error Handling', () {
      testWidgets('should handle network timeouts gracefully', (tester) async {
        // This test would require mocking network conditions
        // or using a test server that can simulate timeouts
        
        logger.i('✅ Network timeout handling test (implementation needed)');
      });

      testWidgets('should handle authentication errors', (tester) async {
        // Create API client with invalid credentials
        final invalidDio = Dio(BaseOptions(
          baseUrl: PaymentTestConfig.currentApiBaseUrl,
          headers: {
            'Authorization': 'Bearer invalid_token',
          },
        ));

        final invalidApiClient = ApiClient(dio: invalidDio, logger: logger);
        final invalidDataSource = PaymentRemoteDataSourceImpl(
          apiClient: invalidApiClient,
          logger: logger,
        );

        final paymentRequest = PaymentRequest(
          amount: PaymentTestConfig.testAmountSmall,
          currency: 'INR',
          invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
          customerId: PaymentTestConfig.testCustomerId,
        );

        // Act & Assert
        expect(
          () => invalidDataSource.createPaymentSession(paymentRequest),
          throwsA(isA<Exception>()),
        );
        
        logger.i('✅ Authentication error handled correctly');
      });
    });

    group('Performance Tests', () {
      testWidgets('should handle concurrent payment session creation', (tester) async {
        const concurrentRequests = 5;
        final futures = <Future>[];

        for (int i = 0; i < concurrentRequests; i++) {
          final paymentRequest = PaymentRequest(
            amount: PaymentTestConfig.testAmountSmall,
            currency: 'INR',
            invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
            customerId: '${PaymentTestConfig.testCustomerId}_$i',
            description: 'Concurrent test payment $i',
          );

          futures.add(dataSource.createPaymentSession(paymentRequest));
        }

        // Act
        final results = await Future.wait(futures);

        // Assert
        expect(results.length, equals(concurrentRequests));
        for (final result in results) {
          expect(result.sessionId, isNotEmpty);
        }
        
        logger.i('✅ Concurrent requests handled successfully');
      });

      testWidgets('should complete payment session creation within timeout', (tester) async {
        final paymentRequest = PaymentRequest(
          amount: PaymentTestConfig.testAmountMedium,
          currency: 'INR',
          invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
          customerId: PaymentTestConfig.testCustomerId,
        );

        // Act
        final stopwatch = Stopwatch()..start();
        final paymentSession = await dataSource.createPaymentSession(paymentRequest);
        stopwatch.stop();

        // Assert
        expect(paymentSession.sessionId, isNotEmpty);
        expect(stopwatch.elapsed, lessThan(const Duration(seconds: 10)));
        
        logger.i('✅ Payment session created in ${stopwatch.elapsed.inMilliseconds}ms');
      });
    });
  });
}

/// Helper class for running manual API tests
class PaymentApiTestRunner {
  static Future<void> runManualTests() async {
    final logger = AppLogger();
    
    logger.i('🧪 Starting manual Zoho Payment API tests...');
    
    try {
      await _testCreatePaymentSession(logger);
      await _testCheckPaymentStatus(logger);
      await _testErrorScenarios(logger);
      
      logger.i('✅ All manual tests completed successfully!');
    } catch (e) {
      logger.e('❌ Manual tests failed: $e');
    }
  }
  
  static Future<void> _testCreatePaymentSession(AppLogger logger) async {
    logger.i('Testing payment session creation...');
    
    // Implementation would go here
    // This is a placeholder for manual testing
  }
  
  static Future<void> _testCheckPaymentStatus(AppLogger logger) async {
    logger.i('Testing payment status checking...');
    
    // Implementation would go here
  }
  
  static Future<void> _testErrorScenarios(AppLogger logger) async {
    logger.i('Testing error scenarios...');
    
    // Implementation would go here
  }
}
