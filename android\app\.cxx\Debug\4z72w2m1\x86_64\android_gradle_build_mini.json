{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\FlutterProjects\\aquapartner\\android\\app\\.cxx\\Debug\\4z72w2m1\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\FlutterProjects\\aquapartner\\android\\app\\.cxx\\Debug\\4z72w2m1\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}