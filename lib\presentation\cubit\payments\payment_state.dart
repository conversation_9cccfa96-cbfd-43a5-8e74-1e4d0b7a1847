import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../../domain/entities/payments/payment_session.dart';
import '../../../domain/entities/payments/payment_transaction.dart';

/// Base class for payment states
abstract class PaymentState extends Equatable {
  const PaymentState();

  @override
  List<Object?> get props => [];
}

/// Initial state when no payment operation is in progress
class PaymentInitial extends PaymentState {
  const PaymentInitial();
}

/// State when payment operation is loading
class PaymentLoading extends PaymentState {
  const PaymentLoading();
}

/// State when payment session has been created successfully
class PaymentSessionCreated extends PaymentState {
  final PaymentSession paymentSession;

  const PaymentSessionCreated({required this.paymentSession});

  @override
  List<Object?> get props => [paymentSession];
}

/// State when payment is being processed (polling for status)
class PaymentProcessing extends PaymentState {
  final String sessionId;
  final PaymentSessionStatus? currentStatus;
  final DateTime startTime;

  const PaymentProcessing({
    required this.sessionId,
    this.currentStatus,
    DateTime? startTime,
  }) : startTime = startTime ?? const PaymentProcessing._defaultTime();

  const PaymentProcessing._defaultTime() : startTime = null;

  PaymentProcessing copyWith({
    String? sessionId,
    PaymentSessionStatus? currentStatus,
    DateTime? startTime,
  }) {
    return PaymentProcessing(
      sessionId: sessionId ?? this.sessionId,
      currentStatus: currentStatus ?? this.currentStatus,
      startTime: startTime ?? this.startTime,
    );
  }

  @override
  List<Object?> get props => [sessionId, currentStatus, startTime];

  /// Gets the duration since processing started
  Duration get processingDuration {
    return DateTime.now().difference(startTime);
  }

  /// Gets user-friendly status message
  String get statusMessage {
    if (currentStatus == null) {
      return 'Processing payment...';
    }
    
    switch (currentStatus!) {
      case PaymentSessionStatus.created:
        return 'Payment session created. Waiting for payment...';
      case PaymentSessionStatus.pending:
        return 'Payment is being processed...';
      case PaymentSessionStatus.completed:
        return 'Payment completed. Verifying...';
      case PaymentSessionStatus.failed:
        return 'Payment failed';
      case PaymentSessionStatus.cancelled:
        return 'Payment cancelled';
      case PaymentSessionStatus.expired:
        return 'Payment session expired';
    }
  }
}

/// State when payment has been completed successfully
class PaymentSuccess extends PaymentState {
  final PaymentTransaction transaction;

  const PaymentSuccess({required this.transaction});

  @override
  List<Object?> get props => [transaction];
}

/// State when payment has failed
class PaymentFailed extends PaymentState {
  final String sessionId;
  final String reason;
  final String? errorCode;

  const PaymentFailed({
    required this.sessionId,
    required this.reason,
    this.errorCode,
  });

  @override
  List<Object?> get props => [sessionId, reason, errorCode];
}

/// State when payment has been cancelled
class PaymentCancelled extends PaymentState {
  final String sessionId;

  const PaymentCancelled({required this.sessionId});

  @override
  List<Object?> get props => [sessionId];
}

/// State when payment session has expired
class PaymentExpired extends PaymentState {
  final String sessionId;

  const PaymentExpired({required this.sessionId});

  @override
  List<Object?> get props => [sessionId];
}

/// State when there's an error in payment operations
class PaymentError extends PaymentState {
  final String message;
  final Failure? failure;

  const PaymentError({
    required this.message,
    this.failure,
  });

  @override
  List<Object?> get props => [message, failure];

  /// Checks if the error is retryable
  bool get isRetryable {
    if (failure == null) return true;
    
    // Network failures are retryable
    if (failure is NetworkFailure) return true;
    
    // Server failures might be retryable
    if (failure is ServerFailure) return true;
    
    // Auth failures require re-authentication
    if (failure is AuthFailure) return false;
    
    // Validation failures are not retryable without fixing the input
    if (failure is ValidationFailure) return false;
    
    return true;
  }

  /// Gets user-friendly error type
  String get errorType {
    if (failure == null) return 'Unknown Error';
    
    if (failure is NetworkFailure) return 'Network Error';
    if (failure is ServerFailure) return 'Server Error';
    if (failure is AuthFailure) return 'Authentication Error';
    if (failure is ValidationFailure) return 'Validation Error';
    
    return 'Unknown Error';
  }
}

/// Extension to check if state represents a terminal payment state
extension PaymentStateExtension on PaymentState {
  bool get isTerminal {
    return this is PaymentSuccess ||
           this is PaymentFailed ||
           this is PaymentCancelled ||
           this is PaymentExpired;
  }

  bool get isLoading {
    return this is PaymentLoading || this is PaymentProcessing;
  }

  bool get isError {
    return this is PaymentError;
  }

  bool get isSuccess {
    return this is PaymentSuccess;
  }

  bool get canRetry {
    if (this is PaymentError) {
      return (this as PaymentError).isRetryable;
    }
    return false;
  }
}
