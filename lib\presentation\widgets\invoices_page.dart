import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/mixins/analytics_mixin.dart';
import 'package:aquapartner/core/utils/currency_formatter.dart';
import 'package:aquapartner/presentation/widgets/loading_widget.dart';
import 'package:aquapartner/presentation/widgets/styled_generic_table.dart';
import 'package:aquapartner/presentation/widgets/zoho_payment_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../domain/entities/invoices/invoice.dart';
import '../cubit/invoices/invoices_cubit.dart';
import '../cubit/invoices/invoices_state.dart';

class InvoicesPage extends StatefulWidget {
  const InvoicesPage({super.key});

  @override
  State<InvoicesPage> createState() => _InvoicesPageState();
}

class _InvoicesPageState extends State<InvoicesPage> with AnalyticsMixin {
  @override
  String get screenName => 'InvoicesPage';

  // Track screen view time
  DateTime? _screenViewStartTime;
  DateTime? _lastInteractionTime;

  // Track user interactions
  int _invoiceTapCount = 0;
  int _scrollCount = 0;

  // Track scroll position
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _screenViewStartTime = DateTime.now();
    _lastInteractionTime = _screenViewStartTime;

    // Add scroll listener for analytics
    _scrollController.addListener(_onScroll);

    context.read<InvoicesCubit>().loadInvoices();

    // Track page initialization
    trackEvent('invoices_page_initialized');
  }

  @override
  void dispose() {
    // Track total time spent on screen
    _trackScreenDuration();

    // Track final engagement metrics
    _trackEngagementMetrics();

    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _trackScreenDuration() {
    if (_screenViewStartTime != null) {
      final duration = DateTime.now().difference(_screenViewStartTime!);
      trackEvent(
        'invoices_screen_duration',
        params: {
          'duration_ms': duration.inMilliseconds.toString(),
          'duration_seconds': duration.inSeconds.toString(),
        },
      );
    }
  }

  void _trackEngagementMetrics() {
    trackEvent(
      'invoices_engagement',
      params: {
        'invoice_tap_count': _invoiceTapCount.toString(),
        'scroll_count': _scrollCount.toString(),
        'time_since_last_interaction':
            _lastInteractionTime != null
                ? DateTime.now()
                    .difference(_lastInteractionTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  void _onScroll() {
    // Don't track every tiny scroll event - only significant ones
    if (!_scrollController.hasClients) return;

    // Track scroll position as percentage
    final scrollPosition = _scrollController.position.pixels;
    final maxScroll = _scrollController.position.maxScrollExtent;

    if (maxScroll <= 0) return;

    final scrollPercentage = (scrollPosition / maxScroll * 100).round();

    // Only track at 25%, 50%, 75%, and 100% scroll positions
    if (scrollPercentage == 25 ||
        scrollPercentage == 50 ||
        scrollPercentage == 75 ||
        scrollPercentage == 100) {
      _scrollCount++;
      _lastInteractionTime = DateTime.now();

      trackEvent(
        'invoices_scrolled',
        params: {
          'scroll_percentage': scrollPercentage.toString(),
          'scroll_position': scrollPosition.toString(),
        },
      );
    }
  }

  void _trackInvoiceTap(Invoice invoice) {
    _invoiceTapCount++;
    _lastInteractionTime = DateTime.now();

    trackUserInteraction(
      'view_invoice_details',
      'table_row',
      elementId: invoice.invoiceNumber,
      additionalParams: {
        'invoice_number': invoice.invoiceNumber,
        'invoice_date': invoice.invoiceDate.toIso8601String(),
        'invoice_amount': invoice.total.toString(),
        'invoice_status': invoice.invoiceStatus,
        'invoice_tap_count': _invoiceTapCount.toString(),
        'time_on_screen_before_tap':
            _screenViewStartTime != null
                ? DateTime.now()
                    .difference(_screenViewStartTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  void _onPaymentComplete(
    bool success,
    String? transactionId,
    Invoice invoice,
  ) {
    _lastInteractionTime = DateTime.now();

    if (success) {
      // Track successful payment
      trackEvent(
        'payment_completed_successfully',
        params: {
          'invoice_number': invoice.invoiceNumber,
          'invoice_amount': invoice.total.toString(),
          'transaction_id': transactionId ?? 'unknown',
          'payment_method': 'zoho_payments',
        },
      );

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Payment successful for invoice ${invoice.invoiceNumber}',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );

        // Refresh invoices to get updated status
        context.read<InvoicesCubit>().loadInvoices();
      }
    } else {
      // Track failed payment
      trackEvent(
        'payment_failed',
        params: {
          'invoice_number': invoice.invoiceNumber,
          'invoice_amount': invoice.total.toString(),
          'transaction_id': transactionId ?? 'unknown',
          'payment_method': 'zoho_payments',
        },
      );

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Payment failed for invoice ${invoice.invoiceNumber}. Please try again.',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<InvoicesCubit, InvoicesState>(
      builder: (context, state) {
        if (state is InvoicesLoaded) {
          return state.invoices.isNotEmpty
              ? SingleChildScrollView(
                controller: _scrollController,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      StyledGenericTable<Invoice>(
                        items: state.invoices,
                        showDividers: true,
                        onRowTap: (invoice) {
                          _trackInvoiceTap(invoice);
                          // Add navigation logic here if needed
                        },
                        columns: [
                          ColumnConfig<Invoice>(
                            title: 'Date',
                            width: 100,
                            cellBuilder:
                                (invoice) => Align(
                                  alignment: Alignment.center,
                                  child: AquaText.body(
                                    DateFormat(
                                      'dd-MM-yyyy',
                                    ).format(invoice.invoiceDate),
                                  ),
                                ),
                          ),
                          ColumnConfig<Invoice>(
                            title: 'Total',
                            width: 100,
                            titleAlignment: Alignment.center,
                            bodyAlignment: Alignment.centerRight,
                            cellBuilder:
                                (invoice) => AquaText.body(
                                  CurrencyFormatter.formatAsINR(
                                    invoice.total,
                                    decimalPlaces: 0,
                                  ),
                                  weight: AquaFontWeight.bold,
                                ),
                          ),
                          ColumnConfig<Invoice>(
                            title: 'Invoice Number',
                            width: 150,
                            cellBuilder:
                                (invoice) => AquaText.body(
                                  invoice.invoiceNumber,
                                  weight: AquaFontWeight.semibold,
                                  color: acPrimaryBlue,
                                ),
                          ),
                          ColumnConfig<Invoice>(
                            title: 'Status',
                            width: 100,
                            cellBuilder:
                                (invoice) =>
                                    invoice.invoiceStatus == 'Overdue'
                                        ? ZohoPaymentButton(
                                          amount:
                                              invoice.balance > 0
                                                  ? invoice.balance
                                                  : invoice.total,
                                          invoiceNumber: invoice.invoiceNumber,
                                          customerId: invoice.customerId,
                                          description:
                                              'Payment for invoice ${invoice.invoiceNumber}',
                                          onPaymentComplete:
                                              (success, transactionId) =>
                                                  _onPaymentComplete(
                                                    success,
                                                    transactionId,
                                                    invoice,
                                                  ),
                                          buttonText: 'Pay Now',
                                        )
                                        : AquaText.body(invoice.invoiceStatus),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              )
              : Center(
                child: AquaText.subheadline("You dont have any invoices"),
              );
        }
        return LoadingWidget(message: "Please wait, your invoices loading.");
      },
      listener: (context, state) {
        // Track state changes for analytics
        if (state is InvoicesLoading) {
          trackEvent('invoices_loading');
        } else if (state is InvoicesLoaded) {
          _lastInteractionTime =
              DateTime.now(); // Update interaction time when data loads
          trackEvent(
            'invoices_loaded',
            params: {
              'invoice_count': state.invoices.length.toString(),
              'is_from_cache': state.isFromCache ? 'true' : 'false',
              'time_to_load':
                  _screenViewStartTime != null
                      ? DateTime.now()
                          .difference(_screenViewStartTime!)
                          .inMilliseconds
                          .toString()
                      : '0',
            },
          );
        } else if (state is InvoiceError) {
          trackEvent(
            'invoices_error',
            params: {'error_message': state.message},
          );
        }
      },
    );
  }
}
