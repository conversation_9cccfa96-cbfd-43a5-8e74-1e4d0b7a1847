import 'package:dio/dio.dart';
import '../services/app_check_service.dart';
import '../utils/logger.dart';
import 'package:flutter/foundation.dart';

/// An interceptor for Dio HTTP client that adds Firebase App Check tokens
/// to outgoing requests for enhanced security.
class AppCheckInterceptor extends Interceptor {
  final AppCheckService _appCheckService;
  final AppLogger _logger;

  AppCheckInterceptor(this._appCheckService, this._logger);

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      // Get App Check token
      final token = await _appCheckService.getToken();

      if (token != null) {
        // Add token to request headers
        options.headers['X-Firebase-AppCheck'] = token;
      } else {
        _logger.w('No App Check token available for request: ${options.uri}');

        // In debug mode, we can add a fake header to avoid backend rejections
        if (kDebugMode) {
          options.headers['X-Firebase-AppCheck-Debug'] = 'debug-mode';
        }
      }
    } catch (e) {
      // Log error but don't block the request
      _logger.e('Error adding App Check token to request', e);
    }

    // Continue with the request
    handler.next(options);
  }
}
