import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/payments/payment_request.dart';
import '../entities/payments/payment_session.dart';
import '../entities/payments/payment_transaction.dart';

/// Repository interface for payment operations
abstract class PaymentRepository {
  /// Creates a new payment session
  Future<Either<Failure, PaymentSession>> createPaymentSession(
    PaymentRequest request,
  );

  /// Retrieves payment session by ID
  Future<Either<Failure, PaymentSession>> getPaymentSession(
    String sessionId,
  );

  /// Verifies payment status and returns transaction details
  Future<Either<Failure, PaymentTransaction>> verifyPayment(
    String sessionId,
  );

  /// Checks payment status without full verification
  Future<Either<Failure, PaymentSession>> checkPaymentStatus(
    String sessionId,
  );

  /// Cancels an active payment session
  Future<Either<Failure, void>> cancelPaymentSession(
    String sessionId,
  );

  /// Gets payment transaction by transaction ID
  Future<Either<Failure, PaymentTransaction>> getPaymentTransaction(
    String transactionId,
  );

  /// Gets all payment transactions for a customer
  Future<Either<Failure, List<PaymentTransaction>>> getCustomerTransactions(
    String customerId, {
    int? limit,
    int? offset,
  });

  /// Gets payment transactions for a specific invoice
  Future<Either<Failure, List<PaymentTransaction>>> getInvoiceTransactions(
    String invoiceNumber,
  );

  /// Processes webhook notification
  Future<Either<Failure, PaymentTransaction>> processWebhookNotification(
    Map<String, dynamic> webhookData,
  );
}
